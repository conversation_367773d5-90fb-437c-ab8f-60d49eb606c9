import React from "react";
import * as Tooltip from '@radix-ui/react-tooltip';
import { HelpCircle } from "lucide-react";

const CustomSlider = ({
  text, // text to display
  unit, // unit of the slider
  defaultValue, // default value of the slider
  min, // minimum value of the slider
  max, // maximum value of the slider
  step, // step value of the slider
  onChange, // function to call when the slider value changes
  tooltip, // tooltip content
}) => {
  const handleChange = (e) => {
    const newValue = Number(e.target.value);
    if (onChange) {
      onChange(newValue);
    }
  };

  // map the color of the slider to the percentage of the slider according to the default value
  // the backgroundcolor of the slider is red and the background color will follow the button position when the button is moved
  const getPercentage = () => {
    return ((defaultValue - min) / (max - min)) * 100;
  };

  const renderTooltip = (content) => (
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <HelpCircle className="text-blue-600 cursor-pointer ml-2 w-5 h-5" />
        </Tooltip.Trigger>
        <Tooltip.Content className="max-w-xs text-sm bg-gray-100 p-3 rounded shadow-lg">
          {content}
        </Tooltip.Content>
      </Tooltip.Root>
    );

  return (
    
    <div className="w-full max-w-xl mx-auto px-4">
      <div className="mb-2">
        <div className="flex flex-row">
        <h3 className="text-lg font-medium text-gray-900">{text}</h3>
        <div className="mt-1.5">
        {tooltip && renderTooltip(tooltip)}
        </div>
        </div>
        <p className="text-2xl font-medium text-red-500 mt-1">
          {defaultValue} {unit}
        </p>
      </div>

      <div className="relative pt-1 pb-6">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={defaultValue}
          onChange={handleChange}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          style={{
            background: `linear-gradient(to right, #ef4444 0%, #ef4444 ${getPercentage()}%, #e5e7eb ${getPercentage()}%, #e5e7eb 100%)`,
          }}
        />

        {/* Custom thumb styling */}
        <style>{`
          input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ef4444;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            cursor: pointer;
          }

          input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ef4444;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            cursor: pointer;
          }
        `}</style>

        <div className="flex justify-between mt-2">
          {/* <span className="text-sm text-gray-700">{min.toFixed(2)}%</span>
          <span className="text-sm text-gray-700">{max.toFixed(2)}%</span> */}
          <span className="text-sm text-gray-700">
            {min}
            {unit}
          </span>
          <span className="text-sm text-gray-700">
            {max}
            {unit}
          </span>
        </div>
      </div>
    </div>
    
  );
};

export default CustomSlider;
