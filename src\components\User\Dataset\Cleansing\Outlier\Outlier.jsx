import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Dropdown from "../dropdown";
import CustomSlider from "../CustomSlider";
import ApplyTable from "../ApplyTable";
import * as Tooltip from '@radix-ui/react-tooltip';
import { HelpCircle } from "lucide-react";

const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

const Outlier = () => {
  const uploadFeatures = useSelector((state) => state.upload.features);
  const dataset = useSelector((state) => state.upload.dataset);

  const [features, setFeatures] = useState([]);
  const [selectedOutlierOption, setSelectedOutlierOption] = useState("");
  const [selectedTrimmingOption, setSelectedTrimmingOption] = useState("");
  const [selectedCappingOption, setSelectedCappingOption] = useState("");
  const [zScore, setZScore] = useState(3);
  const [iqr, setIQR] = useState(1.5);
  const [percentile, setPercentile] = useState({ lower: 1, upper: 99 });
  const [powerTransformation, setPowerTransformation] = useState({
    transformation: "",
    lambda: 1,
  });
  const [showLampda, setShowLampda] = useState(false);
  const [showTable, setShowTable] = useState(false);

  const numericalItems = uploadFeatures.numerical.items;
  const allNumericalItemsMap = {};
  numericalItems.forEach((item) => {
    allNumericalItemsMap[item.id] = item;
  });
  const allNumericalFeatureOrder = numericalItems.map((item) => item.id);

  const renderTooltip = (content) => (
        <Tooltip.Root>
          <Tooltip.Trigger asChild>
            <HelpCircle className="text-blue-600 cursor-pointer ml-2 w-5 h-5" />
          </Tooltip.Trigger>
          <Tooltip.Content className="max-w-xs text-sm bg-gray-100 p-3 rounded shadow-lg">
            {content}
          </Tooltip.Content>
        </Tooltip.Root>
      );

    const getOverallMethodTooltip = (method) => {
      switch (method) {
        case "Trimming":
          return <>
            <strong>Trimming : </strong> Remove outliers from the dataset based on statistical rules. This reduces distortion but also reduces data size.
          </>;
        case "Capping":
          return <>
            <strong>Capping : </strong> Replace extreme outliers with boundary values instead of removing them. This keeps dataset size intact while reducing distortion.
          </>;
        case "Power Transformation":
          return <>
            <strong>Power Transformation : </strong> Apply mathematical transformations to reduce skewness and stabilize variance in the data. Useful for handling non-normal distributions.
          </>;
        default:
          return null;
      }
    };
    
    const getTrimmingCappingMethodTooltip = (method) => {
      switch (method) {
        case "Z-score":
          return <>
            <strong>Z-score : </strong> A statistical measure of how far a value is from the mean in terms of standard deviations. Outliers are detected when the Z-score exceeds the threshold.
          </>;
        case "IQR":
          return <>
            <strong>IQR : </strong> Interquartile Range (IQR) is the spread between the 25th (Q1) and 75th (Q3) percentiles. Outliers are values outside Q1 − k×IQR or Q3 + k×IQR.
          </>;
        case "Percentile":
          return <>
            <strong>Percentile : </strong> Uses percentile thresholds to define boundaries for outliers. Values outside the lower and upper thresholds are treated as outliers.
          </>;
        default:
          return null;
      }
    };

  const allSelectedFeaturesPositive = () => {
    if (features.length === 0) return false;
    return features.every((featureName) => {
      const item = numericalItems.find((f) => f.feature === featureName);
      if (!item || !item.uniqueValue) return false;
      return item.uniqueValue.every((v) => typeof v === "number" && v > 0);
    });
  };

  const handleOptionChange = (value) => setSelectedOutlierOption(value);
  const handleTrimmingOptionChange = (value) => setSelectedTrimmingOption(value);
  const handleCappingOptionChange = (value) => setSelectedCappingOption(value);
  const handleThresholdZscoreValueChange = (value) => setZScore(value);
  const handleThresholdIQRValueChange = (value) => setIQR(value);
  const handlePercentileLowerValue = (value) =>
    setPercentile({ ...percentile, lower: value });
  const handlePercentileUpperValue = (value) =>
    setPercentile({ ...percentile, upper: value });
  const handleLampdaChange = (value) => setShowLampda(value);
  const handleLampdaValueChange = (value) =>
    setPowerTransformation({ ...powerTransformation, lambda: value });

  const handleFeatureCheckboxChange = (featureName, isChecked) => {
    if (isChecked) {
      if (!features.includes(featureName)) setFeatures([...features, featureName]);
    } else {
      setFeatures(features.filter((f) => f !== featureName));
    }
  };

  const handleApply = () => setShowTable(true);
  const handleResetParameters = () => {
    setFeatures([]);
    setSelectedOutlierOption("");
    setSelectedTrimmingOption("");
    setSelectedCappingOption("");
    setZScore(3);
    setIQR(1.5);
    setPercentile({ lower: 1, upper: 99 });
    setPowerTransformation({ transformation: "", lambda: 1 });
    setShowLampda(false);
    setShowTable(false);
  };

  const renderTrimmingContent = () => {
    switch (selectedTrimmingOption) {
      case "Z-score":
        return (
          <CustomSlider
            text="Z-Score Threshold"
            unit=""
            defaultValue={zScore}
            min={1}
            max={5}
            step={1}
            onChange={handleThresholdZscoreValueChange}
          />
        );
      case "IQR":
        return (
          <CustomSlider
            text="IQR Multiplier (k)"
            unit=""
            defaultValue={iqr}
            min={0}
            max={10}
            step={0.5}
            onChange={handleThresholdIQRValueChange}
          />
        );
      case "Percentile":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CustomSlider
              text="Lower Percentile (%)"
              unit=""
              defaultValue={percentile.lower}
              min={0}
              max={50}
              step={1}
              onChange={handlePercentileLowerValue}
            />
            <CustomSlider
              text="Upper Percentile (%)"
              unit=""
              defaultValue={percentile.upper}
              min={50}
              max={100}
              step={1}
              onChange={handlePercentileUpperValue}
            />
          </div>
        );
      default:
        return null;
    }
  };

  const renderCappingContent = () => {
    switch (selectedCappingOption) {
      case "Z-score":
        return (
          <CustomSlider
            text="Z-Score Threshold"
            unit=""
            defaultValue={zScore}
            min={1}
            max={5}
            step={1}
            onChange={handleThresholdZscoreValueChange}
          />
        );
      case "IQR":
        return (
          <CustomSlider
            text="IQR Multiplier (k)"
            unit=""
            defaultValue={iqr}
            min={0}
            max={10}
            step={0.5}
            onChange={handleThresholdIQRValueChange}
          />
        );
      case "Percentile":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CustomSlider
              text="Lower Percentile (%)"
              unit=""
              defaultValue={percentile.lower}
              min={0}
              max={50}
              step={1}
              onChange={handlePercentileLowerValue}
            />
            <CustomSlider
              text="Upper Percentile (%)"
              unit=""
              defaultValue={percentile.upper}
              min={50}
              max={100}
              step={1}
              onChange={handlePercentileUpperValue}
            />
          </div>
        );
      default:
        return null;
    }
  };

  const renderContent = () => {
    switch (selectedOutlierOption) {
      case "Trimming":
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-4">
            <Dropdown
              options={["Z-score", "IQR", "Percentile"]}
              placeholder="Select method"
              tell="Select Trimming Method:"
              searchPlaceholder="Search method..."
              value={selectedTrimmingOption}
              setValue={handleTrimmingOptionChange}
              tooltip1={<>
                <strong>Z-score : </strong>Identify outliers by how many standard deviations they are from the mean.
                <br />
                <strong>IQR : </strong>Detect outliers based on the interquartile range (spread between Q1 and Q3).
                <br />
                <strong>Percentile : </strong>Remove data points below or above selected percentile thresholds.
              </>}
              tooltip2={selectedTrimmingOption ? getTrimmingCappingMethodTooltip(selectedTrimmingOption) : null}
            />
            <div className="mt-4">{renderTrimmingContent()}</div>
          </div>
        );
      case "Capping":
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-4">
            <Dropdown
              options={["Z-score", "IQR", "Percentile"]}
              placeholder="Select method"
              tell="Select Capping Method:"
              searchPlaceholder="Search method..."
              value={selectedCappingOption}
              setValue={handleCappingOptionChange}
              tooltip1={<>
                <strong>Z-score : </strong>Cap values that exceed the specified standard deviation threshold.
                <br />
                <strong>IQR : </strong>Cap values outside the lower and upper IQR bounds.
                <br />
                <strong>Percentile : </strong>Cap values below the lower percentile or above the upper percentile.
              </>}
              tooltip2={selectedTrimmingOption ? getTrimmingCappingMethodTooltip(selectedTrimmingOption) : null}
            />
            <div className="mt-4">{renderCappingContent()}</div>
          </div>
        );
      case "Power Transformation":
        const boxCoxDisabled = !allSelectedFeaturesPositive();
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-4">
            <div className="flex flex-row">
            <h2 className="font-bold text-xl mb-4">Select Transformation</h2>
            <div className="mt-1.5">
            {renderTooltip(<>
              <strong>Yeo-Johnson : </strong> A transformation that can handle both positive and negative values.
              <br />
              <strong>Box-Cox : </strong> A transformation that requires all input values to be positive.
            </>)}
            </div>
            </div>
            <div className="flex gap-6 mb-4">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="transformation"
                  value="Yeo-Johnson"
                  checked={powerTransformation.transformation === "Yeo-Johnson"}
                  onChange={() =>
                    setPowerTransformation({
                      ...powerTransformation,
                      transformation: "Yeo-Johnson",
                    })
                  }
                  className="accent-blue-500"
                />
                Yeo-Johnson
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="transformation"
                  value="Box-Cox"
                  checked={powerTransformation.transformation === "Box-Cox"}
                  onChange={() =>
                    setPowerTransformation({
                      ...powerTransformation,
                      transformation: "Box-Cox",
                    })
                  }
                  disabled={boxCoxDisabled}
                  className="accent-blue-500"
                />
                Box-Cox
              </label>
            </div>
            {boxCoxDisabled && (
              <div className="text-red-500 text-sm mb-2">
                Box-Cox is only available when all selected features have positive values.
              </div>
            )}
            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                id="threshold-checkbox"
                checked={showLampda}
                onChange={() => handleLampdaChange(!showLampda)}
                className="mr-2 accent-blue-500"
              />
              <label htmlFor="threshold-checkbox" className="text-lg">
                Use Lambda (optional)
              </label>
            </div>
            {showLampda && (
              <div className="mt-4">
                <CustomSlider
                  text="Lambda Threshold"
                  unit=""
                  defaultValue={powerTransformation.lambda}
                  min={-5.0}
                  max={5.0}
                  step={0.1}
                  onChange={handleLampdaValueChange}
                  tooltip={<>
                    <strong>Lambda Threshold : </strong> The λ (lambda) parameter controls the strength of the transformation. Default values are estimated automatically, but you can adjust for fine-tuning.
                  </>}
                />
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    if (!showTable) {
      setFeatures([]);
      setSelectedOutlierOption("");
      setSelectedTrimmingOption("");
      setSelectedCappingOption("");
      setZScore(3);
      setIQR(1.5);
      setPercentile({ lower: 1, upper: 99 });
      setPowerTransformation({ transformation: "", lambda: 1 });
      setShowLampda(false);
    }
  }, [showTable]);

  return (
    <div className="mx-4 max-w-7xl">
      <div className="bg-white rounded-xl shadow p-6 mb-6">
        <h2 className="font-bold text-2xl mb-4 text-blue-700">
          Select Numerical Features to Process
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
          {allNumericalFeatureOrder
            .filter((id) => allNumericalItemsMap[id] && !allNumericalItemsMap[id].disabled)
            .map((id) => {
              const item = allNumericalItemsMap[id];
              return (
                <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-3 py-2 shadow">
                  <input
                    type="checkbox"
                    id={item.id}
                    className="accent-blue-500"
                    checked={features.includes(item.feature)}
                    onChange={(e) =>
                      handleFeatureCheckboxChange(item.feature, e.target.checked)
                    }
                  />
                  <span>{item.feature}</span>
                </label>
              );
            })}
        </div>
        <Dropdown
          options={["Trimming", "Capping", "Power Transformation"]}
          placeholder="Select method"
          tell="Select Outlier Handling Method:"
          searchPlaceholder="Search method..."
          value={selectedOutlierOption}
          setValue={handleOptionChange}
          tooltip1={<>
            <strong>About Outlier : </strong>Outliers are extreme values that can distort analysis or model performance. Choose a method to handle them: trimming (remove), cap (replace with limits), or transform values.
          </>}
          tooltip2={selectedOutlierOption ? getOverallMethodTooltip(selectedOutlierOption) : null}
        />
      </div>
      {renderContent()}

        {/* Apply and Reset buttons */}
        <div className="mt-6 flex gap-4 mb-6">
          <button
            onClick={handleApply}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-300"
          >
            Apply Changes
          </button>
          <button
            onClick={handleResetParameters}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 transition-colors duration-300"
          >
            Reset to Default
          </button>
        </div>


      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Outlier"
        />
      )}
    </div>
  );
};

export default Outlier;
