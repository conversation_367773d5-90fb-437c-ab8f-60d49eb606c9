import React, { use, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Dropdown from "../dropdown";
import CustomSlider from "../CustomSlider";
import ConstantImputation from "./ConstantImputation";
import ApplyTable from "../ApplyTable";
import * as Tooltip from '@radix-ui/react-tooltip';
import { HelpCircle } from "lucide-react";

const options = ["Drop Rows", "Drop Columns", "Impute missing value"];
const numericalImputeOptions = [
  "Means Imputation",
  "Median Imputation",
  "Constant Imputation",
];
const CategoricalImputeOptions = [
  "Most Frequent Imputation",
  "Constant Imputation",
];
const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

const MissingValue = () => {
  const [selectedOption, setSelectedOption] = useState("");
  const [dropRowFeatures, setDropRowFeatures] = useState([]);
  const [dropColumnFeatures, setDropColumnFeatures] = useState([]);
  const [numericalMethods, setNumericalMethods] = useState([]);
  const [categoricalMethods, setCategoricalMethods] = useState([]);
  const [showRowThreshold, setShowRowThreshold] = useState(false);
  const [rowThreshold, setRowThreshold] = useState(50);
  const [showColThreshold, setShowColThreshold] = useState(false);
  const [colThreshold, setColThreshold] = useState(50);
  const [showTable, setShowTable] = useState(false);

  const dataset = useSelector((state) => state.upload.dataset);
  const features = useSelector((state) => state.upload.features);

  const numericalItems = features.numerical.items;
  const categoricalItems = [
    ...features.nominal.items,
    ...features.ordinal.items,
  ];

  const allItemsMap = {};
  [
    ...features.unassigned.items,
    ...features.numerical.items,
    ...features.nominal.items,
    ...features.ordinal.items,
  ].forEach((item) => {
    allItemsMap[item.id] = item;
  });

  const allNumericalItemsMap = {};
  numericalItems.forEach((item) => {
    allNumericalItemsMap[item.id] = item;
  });

  const allCategoricalItemsMap = {};
  categoricalItems.forEach((item) => {
    allCategoricalItemsMap[item.id] = item;
  });

  const allFeatureOrder = features.allFeatureOrder || Object.keys(allItemsMap);
  const allNumericalFeatureOrder = numericalItems.map((item) => item.id);
  const allCategoricalFeatureOrder = categoricalItems.map((item) => item.id);

  const renderTooltip = (content) => (
        <Tooltip.Root>
          <Tooltip.Trigger asChild>
            <HelpCircle className="text-blue-600 cursor-pointer ml-2 w-5 h-5" />
          </Tooltip.Trigger>
          <Tooltip.Content className="max-w-xs text-sm bg-gray-100 p-3 rounded shadow-lg">
            {content}
          </Tooltip.Content>
        </Tooltip.Root>
      );
  // Method-specific tooltip content
  const getMethodTooltip = (method) => {
    switch (method) {
      case "Means Imputation":
        return (
          <>
            <strong>Means Imputation:</strong> Replaces missing values with the arithmetic mean (average) of the non-missing values in that column. Best for normally distributed data without outliers.
          </>
        );
      case "Median Imputation":
        return (
          <>
            <strong>Median Imputation:</strong> Replaces missing values with the median (middle value) of the non-missing values in that column. More robust to outliers than mean imputation.
          </>
        );
      case "Constant Imputation":
        return (
          <>
            <strong>Constant Imputation:</strong> Replaces missing values with a specific constant value that you define. Useful when you have domain knowledge about what the missing value should be.
          </>
        );
      case "Most Frequent Imputation":
        return (
          <>
            <strong>Most Frequent Imputation:</strong> Replaces missing values with the most frequently occurring value (mode) in that column. Maintains the distribution of the data.
          </>
        );
      default:
        return null;
    }
  };

  const getOverallMethodTooltip = (method) => {
    switch (method) {
      case "Drop Rows":
        return (
          <>
            <strong>Drop Rows:</strong> Removes rows that contain missing values. Use this when a few rows are missing important data and dropping them won't significantly affect your dataset.
          </>
        );
      case "Drop Columns":
        return (
          <>
            <strong>Drop Columns:</strong> Removes columns that contain missing values. Use this when a feature has too many missing values to be useful.
          </>
        );
      case "Impute missing value":
        return (
          <>
            <strong>Impute missing value:</strong> Fills missing values instead of removing them. You can choose different methods for numerical and categorical features to estimate the missing data.
          </>
        );
      default:
        return null;
    }
  };

  // --- Handlers ---
  const handleOptionChange = (value) => {
    setSelectedOption(value);
    if (value === "Impute missing value") {
      if (numericalMethods.length === 0)
        setNumericalMethods([{ method: "", features: [] }]);
      if (categoricalMethods.length === 0)
        setCategoricalMethods([{ method: "", features: [] }]);
    }
  };

  const handleAddNumericalMethod = () => {
    setNumericalMethods([...numericalMethods, { method: "", features: [] }]);
  };
  const handleAddCategoricalMethod = () => {
    setCategoricalMethods([...categoricalMethods, { method: "", features: [] }]);
  };

  const handleNumericalOptionChange = (value, idx) => {
    const prevMethod = numericalMethods[idx]?.method;
    const shouldReset =
      prevMethod === "Constant Imputation" !== (value === "Constant Imputation");
    setNumericalMethods(
      numericalMethods.map((m, i) =>
        i === idx ? { ...m, method: value, features: shouldReset ? [] : m.features } : m
      )
    );
  };
  const handleCategoricalOptionChange = (value, idx) => {
    const prevMethod = categoricalMethods[idx]?.method;
    const shouldReset =
      prevMethod === "Constant Imputation" !== (value === "Constant Imputation");
    setCategoricalMethods(
      categoricalMethods.map((m, i) =>
        i === idx ? { ...m, method: value, features: shouldReset ? [] : m.features } : m
      )
    );
  };

  const handleNumericalFeaturesChange = (featureId, checked, idx) => {
    setNumericalMethods(
      numericalMethods.map((m, i) => {
        if (i !== idx) return m;
        const newFeatures = checked
          ? [...m.features, featureId]
          : m.features.filter((id) => id !== featureId);
        return { ...m, features: newFeatures };
      })
    );
  };
  const handleCategoricalFeaturesChange = (featureId, checked, idx) => {
    setCategoricalMethods(
      categoricalMethods.map((m, i) => {
        if (i !== idx) return m;
        const newFeatures = checked
          ? [...m.features, featureId]
          : m.features.filter((id) => id !== featureId);
        return { ...m, features: newFeatures };
      })
    );
  };

  const handleDropRowsFeaturesChange = (featureId, checked) => {
    setDropRowFeatures(
      checked
        ? [...dropRowFeatures, featureId]
        : dropRowFeatures.filter((f) => f !== featureId)
    );
  };
  const handleDropColumnFeaturesChange = (featureId, checked) => {
    setDropColumnFeatures(
      checked
        ? [...dropColumnFeatures, featureId]
        : dropColumnFeatures.filter((f) => f !== featureId)
    );
  };

  const handleApply = () => setShowTable(true);
  const handleResetParameters = () => {
    setSelectedOption("");
    setDropRowFeatures([]);
    setDropColumnFeatures([]);
    setNumericalMethods([]);
    setCategoricalMethods([]);
    setShowRowThreshold(false);
    setRowThreshold(50);
    setShowColThreshold(false);
    setColThreshold(50);
    setShowTable(false);
  };

  // --- Renderers ---
  const renderNumericalImputeOptions = (method, idx) => {
    switch (method) {
      case "Means Imputation":
      case "Median Imputation":
        return (
          <div className="mt-2">
            <div className="grid grid-cols-2 gap-2">
              {allNumericalFeatureOrder
                .filter((id) =>
                  allNumericalItemsMap[id] &&
                  !numericalMethods.some((m, i) => {
                    if (i === idx) return false;
                    if (m.method === "Constant Imputation") {
                      return m.features.some((f) => f.feature === cleanUniqueId(id));
                    }
                    return m.features.includes(id);
                  })
                )
                .map((id) => {
                  const item = allNumericalItemsMap[id];
                  const checked = numericalMethods[idx]?.features?.includes(id);
                  return (
                    <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-2 py-1">
                      <input
                        type="checkbox"
                        checked={checked}
                        onChange={(e) => handleNumericalFeaturesChange(item.id, e.target.checked, idx)}
                      />
                      <span>{item.feature}</span>
                    </label>
                  );
                })}
            </div>
          </div>
        );
      case "Constant Imputation":
        return (
          <ConstantImputation
            features={features.numerical.items.filter(
              (item) =>
                !numericalMethods.some((m, i) => {
                  if (i === idx) return false;
                  if (m.method === "Constant Imputation") {
                    return m.features.some((f) => f.feature === cleanUniqueId(item.id));
                  }
                  return m.features.includes(item.id);
                })
            )}
            placeholder="Enter number"
            methodIndex={idx}
            isNumerical={true}
            numericalMissingValueMethods={numericalMethods}
            setNumericalMissingValueMethods={setNumericalMethods}
          />
        );
      default:
        return null;
    }
  };

  const renderCategoricalImputeOptions = (method, idx) => {
    switch (method) {
      case "Most Frequent Imputation":
        return (
          <div className="mt-2">
            <div className="grid grid-cols-2 gap-2">
              {allCategoricalFeatureOrder
                .filter((id) =>
                  allCategoricalItemsMap[id] &&
                  !categoricalMethods.some((m, i) => {
                    if (i === idx) return false;
                    if (m.method === "Constant Imputation") {
                      return m.features.some((f) => f.feature === cleanUniqueId(id));
                    }
                    return m.features.includes(id);
                  })
                )
                .map((id) => {
                  const item = allCategoricalItemsMap[id];
                  const checked = categoricalMethods[idx]?.features?.includes(id);
                  return (
                    <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-2 py-1">
                      <input
                        type="checkbox"
                        checked={checked}
                        onChange={(e) => handleCategoricalFeaturesChange(item.id, e.target.checked, idx)}
                      />
                      <span>{item.feature}</span>
                    </label>
                  );
                })}
            </div>
          </div>
        );
      case "Constant Imputation":
        return (
          <ConstantImputation
            features={[...features.nominal.items, ...features.ordinal.items].filter(
              (item) =>
                !categoricalMethods.some((m, i) => {
                  if (i === idx) return false;
                  if (m.method === "Constant Imputation") {
                    return m.features.some((f) => f.feature === cleanUniqueId(item.id));
                  }
                  return m.features.includes(item.id);
                })
            )}
            placeholder="Enter text or number"
            methodIndex={idx}
            isNumerical={false}
            categoricalMissingValueMethods={categoricalMethods}
            setCategoricalMissingValueMethods={setCategoricalMethods}
          />
        );
      default:
        return null;
    }
  };

  const renderNumericalMethods = () =>
    numericalMethods.map((m, idx) => (
      <div key={idx} className="mb-4 bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <Dropdown
              options={numericalImputeOptions.filter(
                (option) =>
                  !numericalMethods.some((other, i) => i !== idx && other.method === option)
              )}
              placeholder="Select method"
              tell={`Numerical data ${idx + 1}:`}
              searchPlaceholder="Search methods..."
              value={m.method}
              setValue={(value) => handleNumericalOptionChange(value, idx)}
              tooltip2={m.method ? getMethodTooltip(m.method) : null}
            />
            
          </div>
          {idx > 0 && (
            <button
              onClick={() => setNumericalMethods(numericalMethods.filter((_, i) => i !== idx))}
              className="ml-2 p-1.5 text-red-500 hover:text-red-700"
              title="Delete method"
            >
              <svg viewBox="0 0 24 24" className="w-6 h-6">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" />
              </svg>
            </button>
          )}
        </div>
        {m.method && renderNumericalImputeOptions(m.method, idx)}
      </div>
    ));

  const renderCategoricalMethods = () =>
    categoricalMethods.map((m, idx) => (
      <div key={idx} className="mb-4 bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <Dropdown
              options={CategoricalImputeOptions.filter(
                (option) =>
                  !categoricalMethods.some((other, i) => i !== idx && other.method === option)
              )}
              placeholder="Select method"
              tell={`Categorical data ${idx + 1}:`}
              searchPlaceholder="Search methods..."
              value={m.method}
              setValue={(value) => handleCategoricalOptionChange(value, idx)}
              tooltip2={m.method ? getMethodTooltip(m.method) : null}
            />
            
          </div>
          {idx > 0 && (
            <button
              onClick={() => setCategoricalMethods(categoricalMethods.filter((_, i) => i !== idx))}
              className="ml-2 p-1.5 text-red-500 hover:text-red-700"
              title="Delete method"
            >
              <svg viewBox="0 0 24 24" className="w-6 h-6">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" />
              </svg>
            </button>
          )}
        </div>
        {m.method && renderCategoricalImputeOptions(m.method, idx)}
      </div>
    ));

  const renderContent = () => {
    switch (selectedOption) {
      case "Drop Rows":
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-4">
            <h2 className="font-bold text-xl mb-4">Drop Rows</h2>
            <div className="grid grid-cols-2 gap-2 mb-4">
              {allFeatureOrder
                .filter((id) => allItemsMap[id] && !allItemsMap[id].disabled)
                .map((id) => {
                  const item = allItemsMap[id];
                  const checked = dropRowFeatures.includes(id);
                  return (
                    <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-2 py-1">
                      <input
                        type="checkbox"
                        checked={checked}
                        onChange={(e) => handleDropRowsFeaturesChange(item.id, e.target.checked)}
                      />
                      <span>{item.feature}</span>
                    </label>
                  );
                })}
            </div>
            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                checked={showRowThreshold}
                onChange={() => setShowRowThreshold(!showRowThreshold)}
                className="mr-2"
              />
              <label className="text-lg">Use drop row threshold (optional)</label>
            </div>
            {showRowThreshold && (
              <CustomSlider
                text="Drop row threshold"
                unit=" %"
                defaultValue={rowThreshold}
                min={0}
                max={100}
                step={1}
                onChange={setRowThreshold}
                tooltip={<>
                  <strong>Drop Rows Threshold :</strong> Set the percentage threshold for dropping rows. Rows with missing values above this threshold will be removed. Lower values remove fewer rows; higher values remove more.
                </>}
              />
            )}
          </div>
        );
      case "Drop Columns":
        return (
          <div className="bg-white rounded-lg shadow p-6 mt-4">
            <h2 className="font-bold text-xl mb-4">Drop Columns</h2>
            <div className="grid grid-cols-2 gap-2 mb-4">
              {allFeatureOrder
                .filter((id) => allItemsMap[id] && !allItemsMap[id].disabled)
                .map((id) => {
                  const item = allItemsMap[id];
                  const checked = dropColumnFeatures.includes(id);
                  return (
                    <label key={item.id} className="flex items-center gap-2 bg-gray-50 rounded px-2 py-1">
                      <input
                        type="checkbox"
                        checked={checked}
                        onChange={(e) => handleDropColumnFeaturesChange(item.id, e.target.checked)}
                      />
                      <span>{item.feature}</span>
                    </label>
                  );
                })}
            </div>
            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                checked={showColThreshold}
                onChange={() => setShowColThreshold(!showColThreshold)}
                className="mr-2"
              />
              <label className="text-lg">Use drop column threshold (optional)</label>
            </div>
            {showColThreshold && (
              <CustomSlider
                text="Drop column threshold"
                unit=" %"
                defaultValue={colThreshold}
                min={0}
                max={100}
                step={1}
                onChange={setColThreshold}
                tooltip={<>
                  <strong>Drop Columns Threshold :</strong> Set the percentage threshold for dropping columns. Columns with missing values above this threshold will be removed. Lower values keep more columns; higher values remove more.
                </>}
              />
            )}
          </div>
        );
      case "Impute missing value":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            <div>
              <div className="flex flex-row items-center mb-4">
              <h2 className="font-bold text-xl mb-2">Numerical Imputation</h2>
              {renderTooltip(<>
              Select a method to fill missing values in numerical features.
                </>)}
                </div>
              {renderNumericalMethods()}
              <button
                onClick={handleAddNumericalMethod}
                className={`w-full py-2 rounded mt-2 font-semibold ${
                  numericalMethods.length >= 3
                    ? "bg-blue-200 text-gray-500 cursor-not-allowed"
                    : "bg-blue-500 text-white hover:bg-blue-600"
                }`}
                disabled={numericalMethods.length >= 3}
              >
                Add Numerical Method
              </button>
            </div>
            <div>
              <div className="flex flex-row items-center mb-4">
              <h2 className="font-bold text-xl mb-2">Categorical Imputation</h2>
              {renderTooltip(<>
              Select a method to fill missing values in categorical (nominal or ordinal) features.
                </>)}
                </div>
              {renderCategoricalMethods()}
              <button
                onClick={handleAddCategoricalMethod}
                className={`w-full py-2 rounded mt-2 font-semibold ${
                  categoricalMethods.length >= 2
                    ? "bg-blue-200 text-gray-500 cursor-not-allowed"
                    : "bg-blue-500 text-white hover:bg-blue-600"
                }`}
                disabled={categoricalMethods.length >= 2}
              >
                Add Categorical Method
              </button>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    if (!selectedOption) {
      document.querySelectorAll('input[type="checkbox"]').forEach((cb) => (cb.checked = false));
    }
  }, [selectedOption]);

  useEffect(() => {
    console.log("numericalMethods", numericalMethods);

  }, [numericalMethods]);

  return (

    <div className="mx-4 max-w-7xl">
      <div className="bg-white rounded-xl shadow p-6 mb-6">
        <Dropdown
          options={options}
          placeholder="Select method"
          tell="Select missing-value handling method:"
          searchPlaceholder="Search methods..."
          value={selectedOption}
          setValue={handleOptionChange}
          tooltip1={<>Choose how to handle missing data in your dataset. You can remove rows or columns with missing values, or fill them using different imputation methods.</>}
          tooltip2={getOverallMethodTooltip(selectedOption)}
        />
      </div>
      {renderContent()}
      {/* Apply and Reset buttons */}
        <div className="mt-6 flex gap-4 mb-6">
          <button
            onClick={handleApply}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-300"
          >
            Apply Changes
          </button>
          <button
            onClick={handleResetParameters}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 transition-colors duration-300"
          >
            Reset to Default
          </button>
        </div>
      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Missing Value"
        />
      )}
    </div>

  );
};

export default MissingValue;
