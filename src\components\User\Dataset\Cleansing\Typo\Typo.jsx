import React, { useState } from "react";
import CleansingTypos from "./CleansingTypos";
import ApplyTable from "../ApplyTable";
import { useSelector } from "react-redux";

const Typo = () => {
  const [showTable, setShowTable] = useState(false);
  const [resetParameter, setResetParameter] = useState(false);
  const dataset = useSelector((state) => state.upload.dataset);

  const handleApply = () => setShowTable(true);
  const handleResetParameters = () => {
    setResetParameter(true);
    setShowTable(false);
  };

  return (
    <div className="mx-4 max-w-7xl">
      <h1 className="text-2xl font-bold text-blue-700 mb-2 flex items-center gap-2">
        <span className="inline-block w-2 h-8 bg-blue-400 rounded-full mr-2"></span>
        Suggestion
      </h1>

      <CleansingTypos
        resetParameter={resetParameter}
        setResetParameter={setResetParameter}
      />

      {/* Apply and Reset buttons */}
        <div className="mt-6 flex gap-4 mb-6">
          <button
            onClick={handleApply}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-300"
          >
            Apply Changes
          </button>
          <button
            onClick={handleResetParameters}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 transition-colors duration-300"
          >
            Reset to Default
          </button>
        </div>

      {showTable && (
        <ApplyTable originalData={dataset} cleanedData={dataset} tab="Typo" />
      )}
    </div>
  );
};

export default Typo;
