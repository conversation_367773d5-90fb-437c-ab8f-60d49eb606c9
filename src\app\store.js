import { configureStore } from "@reduxjs/toolkit";
import userRoleReducer from "./features/login/userRoleSlice"; // Import the userRole reducer
import progressReducer from "./features/progress/progressSlice"; // Import the progress reducer
import uploadReducer from "./features/progress/upload/uploadSlice"; // Import the upload reducer

export const store = configureStore({
  reducer: {
    userRole: userRoleReducer, // Add the userRole reducer here
    progress: progressReducer, // Add the progress reducer here
    upload: uploadReducer, // Add the upload reducer here
  },
});
