{"name": "react-demos", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/accessibility": "^3.1.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.8", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/vite": "^4.0.13", "axios": "^1.9.0", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "d3": "^7.9.0", "density-clustering": "^1.3.0", "framer-motion": "^12.6.3", "lucide-react": "^0.503.0", "ml-hclust": "^3.1.0", "ml-kmeans": "^6.0.0", "motion": "^12.6.2", "plotly.js": "^3.0.1", "plotly.js-dist-min": "^3.0.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-csv": "^2.2.2", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-plotly.js": "^2.6.0", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "react-tooltip": "^5.29.1", "recharts": "^2.15.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.0.13", "three": "^0.179.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.2.2"}}